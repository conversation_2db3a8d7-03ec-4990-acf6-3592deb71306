diff --git a/node_modules/react-native-snap-carousel/src/carousel/Carousel.js b/node_modules/react-native-snap-carousel/src/carousel/Carousel.js
index dae71a3..4c1c8c1 100644
--- a/node_modules/react-native-snap-carousel/src/carousel/Carousel.js
+++ b/node_modules/react-native-snap-carousel/src/carousel/Carousel.js
@@ -2,6 +2,7 @@ import React, { Component } from 'react';
 import { Animated, Easing, FlatList, I18nManager, Platform, ScrollView, View, ViewPropTypes } from 'react-native';
 import PropTypes from 'prop-types';
 import shallowCompare from 'react-addons-shallow-compare';
+import { ViewStyle } from 'react-native'
 import {
     defaultScrollInterpolator,
     stackScrollInterpolator,
@@ -43,8 +44,16 @@ export default class Carousel extends Component {
         autoplayDelay: PropTypes.number,
         autoplayInterval: PropTypes.number,
         callbackOffsetMargin: PropTypes.number,
-        containerCustomStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
-        contentContainerCustomStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        containerCustomStyle: PropTypes.oneOfType([
+            PropTypes.object,
+            PropTypes.array,
+            PropTypes.number
+        ]),
+        contentContainerCustomStyle: PropTypes.oneOfType([
+            PropTypes.object,
+            PropTypes.array,
+            PropTypes.number
+        ]),
         enableMomentum: PropTypes.bool,
         enableSnap: PropTypes.bool,
         firstItem: PropTypes.number,
@@ -61,7 +70,11 @@ export default class Carousel extends Component {
         scrollEnabled: PropTypes.bool,
         scrollInterpolator: PropTypes.func,
         slideInterpolatedStyle: PropTypes.func,
-        slideStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        slideStyle:  PropTypes.oneOfType([
+            PropTypes.object,
+            PropTypes.array,
+            PropTypes.number
+        ]),
         shouldOptimizeUpdates: PropTypes.bool,
         swipeThreshold: PropTypes.number,
         useScrollView: PropTypes.oneOfType([PropTypes.bool, PropTypes.func]),
@@ -151,9 +164,6 @@ export default class Carousel extends Component {
         this._ignoreNextMomentum = false;
 
         // Warnings
-        if (!ViewPropTypes) {
-            console.warn('react-native-snap-carousel: It is recommended to use at least version 0.44 of React Native with the plugin');
-        }
         if (!props.vertical && (!props.sliderWidth || !props.itemWidth)) {
             console.error('react-native-snap-carousel: You need to specify both `sliderWidth` and `itemWidth` for horizontal carousels');
         }
diff --git a/node_modules/react-native-snap-carousel/src/pagination/Pagination.js b/node_modules/react-native-snap-carousel/src/pagination/Pagination.js
index 5c021cf..ee5fe96 100644
--- a/node_modules/react-native-snap-carousel/src/pagination/Pagination.js
+++ b/node_modules/react-native-snap-carousel/src/pagination/Pagination.js
@@ -3,6 +3,8 @@ import { I18nManager, Platform, View, ViewPropTypes } from 'react-native';
 import PropTypes from 'prop-types';
 import PaginationDot from './PaginationDot';
 import styles from './Pagination.style';
+import { ViewStyle } from 'react-native'
+
 
 const IS_IOS = Platform.OS === 'ios';
 const IS_RTL = I18nManager.isRTL;
@@ -14,16 +16,32 @@ export default class Pagination extends PureComponent {
         dotsLength: PropTypes.number.isRequired,
         activeOpacity: PropTypes.number,
         carouselRef: PropTypes.object,
-        containerStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        containerStyle: PropTypes.oneOfType([
+            PropTypes.object,
+            PropTypes.array,
+            PropTypes.number
+        ]),
         dotColor: PropTypes.string,
-        dotContainerStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        dotContainerStyle: PropTypes.oneOfType([
+            PropTypes.object,
+            PropTypes.array,
+            PropTypes.number
+        ]),
         dotElement: PropTypes.element,
-        dotStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        dotStyle: PropTypes.oneOfType([
+            PropTypes.object,
+            PropTypes.array,
+            PropTypes.number
+        ]),
         inactiveDotColor: PropTypes.string,
         inactiveDotElement: PropTypes.element,
         inactiveDotOpacity: PropTypes.number,
         inactiveDotScale: PropTypes.number,
-        inactiveDotStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        inactiveDotStyle: PropTypes.oneOfType([
+            PropTypes.object,
+            PropTypes.array,
+            PropTypes.number
+        ]),
         renderDots: PropTypes.func,
         tappableDots: PropTypes.bool,
         vertical: PropTypes.bool,
diff --git a/node_modules/react-native-snap-carousel/src/pagination/PaginationDot.js b/node_modules/react-native-snap-carousel/src/pagination/PaginationDot.js
index e59d196..bedbf81 100644
--- a/node_modules/react-native-snap-carousel/src/pagination/PaginationDot.js
+++ b/node_modules/react-native-snap-carousel/src/pagination/PaginationDot.js
@@ -2,6 +2,7 @@ import React, { PureComponent } from 'react';
 import { View, Animated, Easing, TouchableOpacity, ViewPropTypes } from 'react-native';
 import PropTypes from 'prop-types';
 import styles from './Pagination.style';
+import { ViewStyle } from 'react-native'
 
 export default class PaginationDot extends PureComponent {
 
@@ -12,11 +13,23 @@ export default class PaginationDot extends PureComponent {
         activeOpacity: PropTypes.number,
         carouselRef: PropTypes.object,
         color: PropTypes.string,
-        containerStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        containerStyle: PropTypes.oneOfType([
+            PropTypes.object,
+            PropTypes.array,
+            PropTypes.number
+        ]),
         inactiveColor: PropTypes.string,
-        inactiveStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        inactiveStyle: PropTypes.oneOfType([
+            PropTypes.object,
+            PropTypes.array,
+            PropTypes.number
+        ]),
         index: PropTypes.number,
-        style: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        style: PropTypes.oneOfType([
+            PropTypes.object,
+            PropTypes.array,
+            PropTypes.number
+        ]),
         tappable: PropTypes.bool
     };
 
diff --git a/node_modules/react-native-snap-carousel/src/parallaximage/ParallaxImage.js b/node_modules/react-native-snap-carousel/src/parallaximage/ParallaxImage.js
index 8bc774a..c9c2578 100644
--- a/node_modules/react-native-snap-carousel/src/parallaximage/ParallaxImage.js
+++ b/node_modules/react-native-snap-carousel/src/parallaximage/ParallaxImage.js
@@ -4,6 +4,8 @@ import React, { Component } from 'react';
 import { View, ViewPropTypes, Image, Animated, Easing, ActivityIndicator, findNodeHandle } from 'react-native';
 import PropTypes from 'prop-types';
 import styles from './ParallaxImage.style';
+import { ViewStyle } from 'react-native'
+
 
 export default class ParallaxImage extends Component {
 
@@ -16,7 +18,11 @@ export default class ParallaxImage extends Component {
         sliderHeight: PropTypes.number, // passed from <Carousel />
         sliderWidth: PropTypes.number, // passed from <Carousel />
         vertical: PropTypes.bool, // passed from <Carousel />
-        containerStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        containerStyle: PropTypes.oneOfType([
+            PropTypes.object,
+            PropTypes.array,
+            PropTypes.number
+        ]),
         dimensions: PropTypes.shape({
             width: PropTypes.number,
             height: PropTypes.number
