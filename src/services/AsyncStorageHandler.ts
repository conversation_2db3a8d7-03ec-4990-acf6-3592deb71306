import AsyncStorage from "@react-native-async-storage/async-storage";
import { store } from "../redux/store";
import { Dispatch } from "redux";
import { updateAppID } from "../redux/AuthSlice";
import { AppService } from "./AppService";

export class AsyncStorageHandler {
  static async loadAppId(dispatch: Dispatch): Promise<void> {
    try {
      const storedAppId = (await AsyncStorage.getItem("appId")) ?? "";
      console.log("storedAppId---->", storedAppId);
      
      dispatch(updateAppID(storedAppId));
    } catch (error) {
      console.error("Failed to load App ID", error);
    }
  }

  static async setAppId(appId: string, dispatch: Dispatch): Promise<void> {
    try {
      await AsyncStorage.setItem("appId", appId);
      AppService.appId = appId;
      dispatch(updateAppID(appId));
    } catch (error) {
      console.error("Failed to set App ID", error);
    }
  }
}
