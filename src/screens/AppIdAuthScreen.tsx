import React, { useState, useLayoutEffect } from "react";
import {
  View,
  TextInput,
  Text,
  TouchableOpacity,
  StyleSheet,
  Keyboard,
  TouchableWithoutFeedback,
  ActivityIndicator,
  StatusBar,
} from "react-native";
import { CommonActions, useNavigation } from "@react-navigation/native";
import { AsyncStorageHandler } from "../services/AsyncStorageHandler";
import { AppDispatch, RootState } from "../redux/store";
import { useDispatch, useSelector } from "react-redux";
import { AppService } from "../services/AppService";
import { SafeAreaProvider } from "react-native-safe-area-context";
import ErrorScreen from "./ErrorScreen";
import { AuthScreenConstsAr, AuthScreenConstsEn } from "../utils/constants";
import { Alert } from "react-native";

const AppIdAuthScreen: React.FC = (props) => {
  const [appId, setAppId] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const isRTL = useSelector((state: RootState) => state.homeScreen.isRTL);
  const [initialRTL, setInitialRTL] = useState(isRTL); // Store initial RTL state
  const navigation = useNavigation();
  const dispatch = useDispatch<AppDispatch>();

  // Set initial RTL direction once when component mounts
  useLayoutEffect(() => {
    setInitialRTL(isRTL);
  }, []);

  const handleSubmit = async () => {
    console.log("App ID Submitted:", appId);
    try {
      setIsLoading(true);
      await AsyncStorageHandler.setAppId(appId, dispatch);
      await AppService.getFullAppSetup(dispatch);
      await AppService.imageKeyId(dispatch);
      await AppService.getHomeScreenSetup(dispatch);  
      const darkMode = await AppService.getItemValue();
      await AppService.color(dispatch, darkMode);
      console.log("App ID saved successfully!");
      // navigation.replace("Home"); // Navigate to HomeScreen
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: 'Home' }], // Replace with your home screen
        })
      );
    } catch (error) {
      setIsLoading(false);
      console.error("Failed to save App ID", error);
      Alert.alert(
        initialRTL ? "خطأ" : "Error",
        initialRTL ? "معرف التطبيق غير صالح. يرجى المحاولة مرة أخرى." : "Invalid App ID. Please try again.",
        [{ text: initialRTL ? "حسناً" : "OK" }]
      );
    } finally {
      // setIsLoading(false);
    }
  };

  return (
    <SafeAreaProvider>
      <StatusBar barStyle="dark-content" />
      <View style={{ flex: 1, direction: initialRTL ? 'rtl' : 'ltr' }}>
        {isLoading && (
          <View
            style={{
              ...StyleSheet.absoluteFillObject,
              backgroundColor: 'rgba(255,255,255,0.5)',
              justifyContent: 'center',
              alignItems: 'center',
              zIndex: 2,
              direction: initialRTL ? 'rtl' : 'ltr', // Use initialRTL
            }}
            pointerEvents="auto"
          >
            <ActivityIndicator size="large" color="black" />
          </View>
        )}

        <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
          <View style={[styles(initialRTL).container]}>
            <Text style={[styles(initialRTL).label]}>
              {initialRTL ? AuthScreenConstsAr.title : AuthScreenConstsEn.title}
            </Text>
            <TextInput
              style={[
                styles(initialRTL).input,
                { writingDirection: initialRTL ? 'rtl' : 'ltr' }
              ]}
              placeholder={initialRTL ? AuthScreenConstsAr.appId : AuthScreenConstsEn.appId}
              value={appId}
              onChangeText={setAppId}
              keyboardType="default"
              returnKeyType="done"
              textAlign={initialRTL ? 'right' : 'left'}
            />

            <TouchableOpacity
              style={[
                styles(initialRTL).button,
                appId.trim() === "" && styles(initialRTL).buttonDisabled,
              ]}
              onPress={handleSubmit}
              disabled={appId.trim() === ""}
            >
              <Text style={styles(initialRTL).buttonText}>
                {initialRTL ? AuthScreenConstsAr.submit : AuthScreenConstsEn.submit}
              </Text>
            </TouchableOpacity>
          </View>
        </TouchableWithoutFeedback>
      </View>
    </SafeAreaProvider>
  );
};

// Update styles to use a more explicit RTL approach
const styles = (isRTL: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    padding: 20,
    backgroundColor: "#f5f5f5",
    direction: isRTL ? 'rtl' : 'ltr',
  },
  label: {
    fontSize: 18,
    marginBottom: 10,
    fontWeight: "bold",
    color: "#333",
    textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
  },
  input: {
    direction: isRTL ? 'rtl' : 'ltr',
    width: "100%",
    height: 50,
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
    paddingHorizontal: 10,
    backgroundColor: "#fff",
    marginBottom: 20,
    fontSize: 16,
    color: 'black',
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  },
  button: {
    width: "100%",
    height: 50,
    backgroundColor: "#007BFF",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5, // For Android shadow effect
  },
  buttonDisabled: {
    backgroundColor: "#A9A9A9", // Gray when disabled
  },
  buttonText: {
    fontSize: 18,
    color: "#fff",
    fontWeight: "bold",
    textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
  },
});

export default AppIdAuthScreen;
