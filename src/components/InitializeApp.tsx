import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { AppService } from '../services/AppService';
import { AppDispatch } from '../redux/store';

const InitializeApp: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    const initializeApp = async () => {
      try {
        await AppService.getFullAppSetup(dispatch);
        await AppService.imageKeyId(dispatch); 
        
        const darkMode = await AppService.getItemValue();
        await AppService.color(dispatch, darkMode);
      } catch (error) {
        console.error('Error initializing app:', error);
      }
    };

    initializeApp();
  }, [dispatch]);

  return null; // This component only performs initialization, so no UI is needed
};

export default InitializeApp;
