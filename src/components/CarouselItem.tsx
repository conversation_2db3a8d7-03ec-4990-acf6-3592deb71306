import React from 'react';
import { View, StyleSheet, Text, Dimensions, TouchableOpacity } from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';
import { ActivityIndicator } from 'react-native';
import { Image } from 'react-native-elements';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { AppService } from '../services/AppService';
import LinearGradient from 'react-native-linear-gradient';

const { width, height } = Dimensions.get('window');

const CarouselItem = (props) => {
  const navigation = useNavigation();
  const color = useSelector((state: RootState) => state.homeScreen.colors);

  const styles = StyleSheet.create({
    row: {
      flex: 1,
      flexDirection: 'row',
      shadowColor: 'white',
      shadowOffset: { width: 0.8, height: -0.8 },
      shadowOpacity: 10,
      shadowRadius: 10,
    },
    inputWrap: {
      flex: 1,
      borderColor: color.primaryTextColor,
    },

    View1: {
      position: 'absolute',
      alignItems: 'center',
      bottom: width / 10,
      marginHorizontal: width / 15,
    },
    View2: {
      position: 'absolute',
      alignItems: 'center',
      bottom: width / 10,
      textAlign: 'right',
      marginHorizontal: width / 15,
    },
    image: {

      // backgroundColor: 'transparent',
      backgroundColor: 'linear-gradient(180deg, white, black)',
      width: '100%',
      height: '97%',
      // width: width,
      // height: height / 1.2, // Increase height to shift crop
      // position: "absolute",
      top: 0, // 
      // bottom:10,
      //The multiplication factor 0.5625 is significant because it represents the aspect ratio of 16:9, which is a common aspect ratio for videos and images. By multiplying the width by 0.5625, the code ensures that the height maintains this aspect ratio relative to the width. This is useful for creating responsive designs where the dimensions of elements need to adjust proportionally to different screen sizes or container widths.
    },
    itemTitle: {
      color: color.primaryTextColor,
      fontSize: width / 40,
      marginBottom: 5,
      fontWeight: 'bold',
      elevation: 5,
    },
    gradient: {
      position: 'absolute',
      left: 0,
      right: 0,
      bottom: 0,
      height: 100,
    },
  });

  console.log(props.item.imageUrl);

  console.log("url", props.item.imageUrl);

  return (
    <View style={{
      marginTop: width / 40,
      height: height / 1.6,
      position: 'relative',
      overflow: 'hidden',
    }}>
      {props.item.imageUrl != null ? (
        <Image
          PlaceholderContent={<ActivityIndicator />}
          style={styles.image}
          source={{
            uri: props.item.imageUrl != '' ? props.item.imageUrl : null,
          }}
          resizeMode='cover'
          containerStyle={{ width: width }}

        />

      ) : null}
      {/* <LinearGradient
        colors={['transparent', 'rgba(0,0,0,1)']}
        style={styles.gradient}
      /> */}
      {/* {props.item.movieid != '' ? (
        <TouchableOpacity
          style={styles.View1}
          onPress={() => {
            navigation.push('FullScreen', {
              itemurl: props.item.movieid,
              image: props.item.videoimage,
            });
          }}>
          <MaterialCommunityIcons
            name="play-circle-outline"
            color={color.primaryTextColor}
            size={width / 10}
          />
          <Text style={styles.itemTitle}>PLAY NOW</Text>
        </TouchableOpacity>
      ) : null}
      {props.item.trailerid != '' ? (
        <TouchableOpacity
          style={styles.View2}
          onPress={() => {
            navigation.push('FullScreen', {
              itemurl: props.item.trailerid,
              image: props.item.videoimage,
            });
          }}>
          {console.log('hfdsgvew', props.item.trailerid)}
          <MaterialCommunityIcons
            name="play-circle-outline"
            color={color.primaryTextColor}
            size={width / 10}
          />
          <Text style={styles.itemTitle}>TRAILER</Text>
        </TouchableOpacity>
      ) : null} */}
    </View>
  );
};

export default CarouselItem;
